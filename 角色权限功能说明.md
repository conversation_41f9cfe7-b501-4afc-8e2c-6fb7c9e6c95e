# 系统角色权限与功能说明

## 概述

本系统共设置了四个主要角色，每个角色具有不同的权限范围和功能模块访问权限。系统采用基于角色的访问控制（RBAC）机制，确保用户只能访问其角色权限范围内的功能。

---

## 1. 系统管理员 (ADMIN)

### 角色描述
系统管理员是系统的最高权限角色，负责整个系统的用户管理、角色配置和系统维护工作。

### 主要权限
- **用户管理**：创建、编辑、删除用户账号，管理用户状态
- **角色管理**：配置系统角色，分配角色权限
- **系统配置**：管理系统字典、参数配置等基础数据

### 功能模块

#### 账号管理
- 用户账号的创建、编辑、删除
- 用户状态管理（启用/禁用/锁定）
- 用户角色分配和权限管理
- 用户信息查看和维护

#### 角色管理
- 系统角色的创建和配置
- 角色权限的分配和回收
- 角色与功能模块的映射关系管理
- 角色状态管理

#### 字典管理
- 系统字典数据的维护
- 字典值的增删改查
- 字典分类管理
- 系统参数配置

### 访问路径
- 主入口：`/background`
- 默认页面：账号管理

---

## 2. 数据资源管理员 (RESOURCE_OPERATOR)

### 角色描述
数据资源管理员负责系统中数据资源的管理，包括数据集管理、数据清洗、数据脱敏等数据治理相关工作。

### 主要权限
- **数据集管理**：数据集的创建、导入、管理和维护
- **数据脱敏**：敏感数据的脱敏处理和管理

### 功能模块

#### 数据集管理
- 数据集的创建和导入
- 数据库连接和数据导入
- 数据集元数据管理
- 数据类型映射配置

#### 数据脱敏
- 敏感数据识别和处理

### 访问路径
- 主入口：`/intelligent`
- 默认页面：数据资源管理首页

---

## 3. 用户业务管理员 (USER_OPERATOR)

### 角色描述
用户业务管理员负责处理用户的业务申请，包括新用户注册审核、机构注册审核、项目申请处理等业务流程管理。

### 主要权限
- **用户注册审核**：新用户注册申请的审核和处理
- **机构注册审核**：新机构注册申请的审核和管理
- **项目申请处理**：用户项目申请的审核和管理
- **业务流程管理**：各类业务流程的监控和处理

### 功能模块

#### 用户业务管理
- 新用户注册申请审核
- 用户信息变更审核
- 用户状态管理

#### 机构管理
- 新机构注册申请审核
- 机构信息维护
- 机构状态管理
- 机构业务处理

#### 项目管理
- 项目申请审核
- 项目信息管理
- 项目状态跟踪
- 项目资源分配

### 访问路径
- 主入口：`/business`
- 默认页面：用户业务管理

---

## 4. 项目管理员 (CUSTOMER)

### 角色描述
项目管理员是普通用户角色，主要负责自己参与项目的管理，包括项目申请、项目管理和相关业务处理。

### 主要权限
- **项目申请**：提交新项目申请
- **项目管理**：管理自己负责或参与的项目
- **数据访问**：访问项目相关的数据资源
- **个人信息管理**：维护个人账号信息

### 功能模块

#### 项目申请
- 新项目申请提交
- 项目申请表填写
- 项目申请状态查看
- 申请材料上传

#### 项目管理
- 项目信息查看和编辑
- 项目进度管理
- 项目团队管理
- 项目数据管理

#### 个人中心
- 个人信息维护
- 密码修改
- 账号安全设置
- 操作日志查看

### 访问路径
- 主入口：`/personal`
- 默认页面：个人工作台

---

## 角色权限矩阵

| 功能模块 | 系统管理员 | 数据资源管理员 | 用户业务管理员 | 项目管理员 |
|---------|-----------|---------------|---------------|-----------|
| 用户管理 | ✅ 完全权限 | ❌ | ✅ 审核权限 | ❌ |
| 角色管理 | ✅ 完全权限 | ❌ | ❌ | ❌ |
| 数据集管理 | ❌ | ✅ 完全权限 | ❌ | ✅ 查看权限 |
| 数据脱敏 | ❌ | ✅ 完全权限 | ❌ | ❌ |
| 项目审核 | ❌ | ❌ | ✅ 完全权限 | ❌ |
| 项目管理 | ❌ | ❌ | ✅ 管理权限 | ✅ 参与权限 |
| 系统配置 | ✅ 完全权限 | ❌ | ❌ | ❌ |

---

## 注意事项

1. **角色互斥性**：用户可以同时拥有多个角色，系统会根据用户的角色组合显示相应的功能入口
2. **权限继承**：某些权限具有层级关系，高级权限包含低级权限的功能
3. **动态路由**：系统根据用户角色动态加载对应的路由和菜单
4. **安全控制**：所有操作都会进行权限验证，确保用户只能执行其权限范围内的操作

---

## 技术实现

- **权限控制**：基于角色代码（RoleCode）进行路由和功能访问控制
- **动态路由**：根据用户角色动态挂载对应的路由模块
- **菜单显示**：根据角色权限动态显示可访问的菜单项
- **API权限**：后端API同样基于角色进行权限验证
