# 系统角色权限与功能说明

## 概述

本系统共设置了四个主要角色，每个角色具有不同的权限范围和功能模块访问权限。系统采用基于角色的访问控制（RBAC）机制，确保用户只能访问其角色权限范围内的功能。

---

## 1. 系统管理员 (ADMIN)

### 角色描述
系统管理员是系统的最高权限角色，负责整个系统的用户管理、角色配置和系统维护工作。

### 主要权限
- **用户管理**：创建、编辑、删除用户账号，管理用户状态
- **角色管理**：配置系统角色，分配角色权限
- **系统配置**：管理系统字典、参数配置等基础数据

### 功能模块

#### 账号管理
- 用户账号的创建、编辑、删除
- 用户状态管理（启用/禁用/锁定）
- 用户角色分配和权限管理
- 用户信息查看和维护

#### 角色管理
- 系统角色的创建和配置
- 角色权限的分配和回收
- 角色与功能模块的映射关系管理
- 角色状态管理

#### 字典管理
- 系统字典数据的维护
- 字典值的增删改查
- 字典分类管理
- 系统参数配置

### 访问路径
- 主入口：`/background`
- 默认页面：账号管理

---

## 2. 数据资源管理员 (RESOURCE_OPERATOR)

### 角色描述
数据资源管理员负责系统中数据资源的管理，包括数据集管理、数据清洗、数据脱敏等数据治理相关工作。

### 主要权限
- **数据集管理**：数据集的创建、导入、管理和维护
- **数据脱敏**：敏感数据的脱敏处理和管理

### 功能模块

#### 数据集管理
- 数据集的创建和导入
- 数据库连接和数据导入
- 数据集元数据管理
- 数据类型映射配置

#### 数据脱敏
- 敏感数据识别和处理

### 访问路径
- 主入口：`/intelligent`
- 默认页面：数据资源管理首页

---

## 3. 用户业务管理员 (USER_OPERATOR)

### 角色描述
用户业务管理员负责处理用户的业务申请，包括新用户注册审核、机构注册审核、项目申请处理等业务流程管理。

### 主要权限
- **用户注册审核**：新用户注册申请的审核和处理
- **机构注册审核**：新机构注册申请的审核和管理
- **项目申请处理**：用户项目申请的审核和管理

### 功能模块

#### 用户业务管理
- 新用户注册申请审核
- 用户信息变更审核
- 用户状态管理

#### 机构管理
- 新机构注册申请审核
- 机构信息维护
- 机构状态管理
- 机构业务处理

#### 项目管理
- 项目申请审核
- 项目信息管理
- 项目状态跟踪
- 项目资源分配

### 访问路径
- 主入口：`/business`
- 默认页面：用户业务管理

---

## 4. 项目管理员 (CUSTOMER)

### 角色描述
项目管理员是普通用户角色，主要负责自己参与项目的管理，包括项目申请、项目管理和相关业务处理。在项目管理员角色下，根据在具体项目中的职责不同，又分为项目创建者和项目成员两个子角色。

### 主要权限
- **项目申请**：提交新项目申请
- **项目管理**：管理自己负责或参与的项目
- **数据访问**：访问项目相关的数据资源
- **个人信息管理**：维护个人账号信息

### 功能模块

#### 项目申请
- 新项目申请提交
- 项目申请表填写
- 项目申请状态查看
- 申请材料上传

#### 项目管理
- 项目信息查看和编辑
- 项目进度管理
- 项目成员管理
- 项目数据管理

#### 个人中心
- 个人信息维护
- 密码修改
- 账号安全设置
- 操作日志查看

### 访问路径
- 主入口：`/personal`
- 默认页面：个人工作台

---

### 4.1 项目创建者 (ApplicationAdministrator)

#### 角色描述
项目创建者是项目的负责人，拥有项目的完全管理权限，负责项目的创建、配置、成员管理和整体运营。

#### 主要权限
- **项目完全控制**：创建、编辑、删除项目
- **成员管理**：添加、移除项目成员，分配成员角色
- **项目配置**：设置项目参数、权限配置
- **数据管理**：管理项目相关的数据资源和访问权限
- **项目提交**：提交项目申请进行审核

#### 功能特权
- 可以编辑项目基本信息
- 可以管理项目团队成员
- 可以分配和调整成员角色
- 可以提交项目申请
- 可以删除项目（草稿状态）
- 拥有项目数据的完全访问权限

---

### 4.2 项目成员 (ApplicationUser)

#### 角色描述
项目成员是参与项目的普通用户，具有有限的项目访问权限，主要负责执行项目任务和使用项目资源。

#### 主要权限
- **项目查看**：查看项目基本信息和进度
- **数据访问**：访问被授权的项目数据资源
- **任务执行**：执行分配的项目任务
- **信息更新**：更新个人在项目中的工作状态

#### 功能限制
- 只能查看项目信息，无法编辑
- 无法管理其他项目成员
- 无法修改项目配置
- 无法提交项目申请
- 只能访问被授权的数据资源

---

## 角色权限矩阵

### 系统级角色权限对比

| 功能模块 | 系统管理员 | 数据资源管理员 | 用户业务管理员 | 项目管理员 |
|---------|-----------|---------------|---------------|-----------|
| 用户管理 | ✅ 完全权限 | ❌ | ✅ 审核权限 | ❌ |
| 角色管理 | ✅ 完全权限 | ❌ | ❌ | ❌ |
| 数据集管理 | ❌ | ✅ 完全权限 | ❌ | ✅ 查看权限 |
| 数据脱敏 | ❌ | ✅ 完全权限 | ❌ | ❌ |
| 项目审核 | ❌ | ❌ | ✅ 完全权限 | ❌ |
| 项目管理 | ❌ | ❌ | ✅ 管理权限 | ✅ 参与权限 |
| 系统配置 | ✅ 完全权限 | ❌ | ❌ | ❌ |

### 项目级角色权限对比

| 功能模块 | 项目创建者 | 项目成员 |
|---------|-----------|----------|
| 项目信息编辑 | ✅ 完全权限 | ❌ 仅查看 |
| 项目成员管理 | ✅ 完全权限 | ❌ |
| 项目角色分配 | ✅ 完全权限 | ❌ |
| 项目申请提交 | ✅ 完全权限 | ❌ |
| 项目删除 | ✅ 草稿状态可删除 | ❌ |
| 项目数据访问 | ✅ 完全权限 | ✅ 授权范围内 |
| 项目进度查看 | ✅ 完全权限 | ✅ 完全权限 |
| 个人任务管理 | ✅ 完全权限 | ✅ 完全权限 |

---

## 注意事项

1. **角色层级结构**：系统采用两级角色体系
   - **系统级角色**：ADMIN、RESOURCE_OPERATOR、USER_OPERATOR、CUSTOMER，控制系统功能模块的访问权限
   - **项目级角色**：ApplicationAdministrator（项目创建者）、ApplicationUser（项目成员），控制具体项目内的操作权限

2. **角色组合**：用户可以同时拥有多个系统级角色，系统会根据用户的角色组合显示相应的功能入口

3. **项目角色分配**：在具体项目中，用户会被分配项目级角色，项目创建者可以管理项目成员的角色分配

4. **权限继承**：某些权限具有层级关系，高级权限包含低级权限的功能

5. **动态路由**：系统根据用户的系统级角色动态加载对应的路由和菜单

6. **安全控制**：所有操作都会进行权限验证，确保用户只能执行其权限范围内的操作

7. **项目权限隔离**：项目级权限仅在对应项目范围内有效，不同项目之间的权限相互独立

---

## 技术实现

- **权限控制**：基于角色代码（RoleCode）进行路由和功能访问控制
- **动态路由**：根据用户角色动态挂载对应的路由模块
- **菜单显示**：根据角色权限动态显示可访问的菜单项
- **API权限**：后端API同样基于角色进行权限验证
